import { Injectable } from '@angular/core';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

@Injectable({
  providedIn: 'root'
})
export class JsonQueryService {

  constructor(private unviredCordovaSDK: UnviredCordovaSDK) { }

  /**
   * Universal JSON query method using LIKE patterns
   * Compatible with all Android versions (13, 14, 15+)
   * 
   * @param tableName - Database table name
   * @param jsonColumn - Column containing JSON data
   * @param filters - Object with field-value pairs to filter by
   * @param additionalWhere - Additional WHERE conditions
   * @param orderBy - ORDER BY clause
   * @returns Promise with parsed results
   */
  async queryJsonData(
    tableName: string,
    jsonColumn: string,
    filters: {[key: string]: string | number} = {},
    additionalWhere: string = '',
    orderBy: string = ''
  ): Promise<any[]> {
    
    try {
      // Build LIKE conditions for JSON filtering
      const likeConditions = Object.entries(filters).map(([field, value]) => {
        // Escape quotes in values to prevent SQL injection
        const escapedValue = String(value).replace(/'/g, "''");
        return `${jsonColumn} LIKE '%"${field}":"${escapedValue}"%'`;
      });

      // Combine all WHERE conditions
      const whereConditions = [];
      if (likeConditions.length > 0) {
        whereConditions.push(...likeConditions);
      }
      if (additionalWhere) {
        whereConditions.push(additionalWhere);
      }

      // Build final query
      let query = `SELECT * FROM ${tableName}`;
      if (whereConditions.length > 0) {
        query += ` WHERE ${whereConditions.join(' AND ')}`;
      }
      if (orderBy) {
        query += ` ORDER BY ${orderBy}`;
      }

      console.log('JSON Query:', query);

      // Execute query
      const result = await this.unviredCordovaSDK.dbExecuteStatement(query);

      if (result.type === ResultType.success && result.data) {
        // Parse JSON data in application
        return result.data.map(row => {
          try {
            if (row[jsonColumn]) {
              const parsedData = JSON.parse(row[jsonColumn]);
              return { ...row, [`${jsonColumn}_PARSED`]: parsedData };
            }
            return row;
          } catch (error) {
            console.error(`JSON parsing error for ${tableName}.${jsonColumn}:`, error);
            return { ...row, [`${jsonColumn}_PARSED`]: {} };
          }
        });
      }

      return [];
    } catch (error) {
      console.error('JsonQueryService error:', error);
      return [];
    }
  }

  /**
   * Specialized method for LMD queries
   */
  async queryLMDData(filters: {
    asset?: string;
    accountNo?: string;
    certNum?: string;
    rps?: string;
    portName?: string;
    terminalType?: string;
    shipSide?: string;
  }, additionalWhere: string = '', orderBy: string = 'CREATED_DATE DESC'): Promise<any[]> {
    
    return this.queryJsonData('LMD_HEADER', 'LMD_DATA', filters, additionalWhere, orderBy);
  }

  /**
   * Specialized method for Measurement queries
   */
  async queryMeasurementData(filters: {
    id?: string;
    type?: string;
    inspectionId?: string;
  }, additionalWhere: string = '', orderBy: string = 'MEAS_TIMESTAMP DESC'): Promise<any[]> {
    
    return this.queryJsonData('MEASUREMENT', 'DATA', filters, additionalWhere, orderBy);
  }

  /**
   * Helper method to safely extract nested JSON values
   */
  extractNestedValue(obj: any, path: string, defaultValue: any = ''): any {
    try {
      return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
    } catch (error) {
      console.error('Error extracting nested value:', error);
      return defaultValue;
    }
  }

  /**
   * Helper method to build complex LIKE patterns for arrays
   */
  buildArrayLikePattern(jsonColumn: string, arrayField: string, searchValue: string): string {
    // For searching within JSON arrays: "arrayField":[{"key":"value"}]
    return `${jsonColumn} LIKE '%"${arrayField}":%"${searchValue}"%'`;
  }
}
