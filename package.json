{"name": "samson-inspections", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "postinstall": "node scripts/postinstall.js"}, "private": true, "dependencies": {"@angular/animations": "^18.2.2", "@angular/cdk": "^18.2.2", "@angular/common": "^18.2.2", "@angular/compiler": "^18.2.2", "@angular/core": "^18.2.2", "@angular/forms": "^18.2.2", "@angular/material": "^18.2.3", "@angular/material-moment-adapter": "^18.2.3", "@angular/platform-browser": "^18.2.2", "@angular/platform-browser-dynamic": "^18.2.2", "@angular/router": "^18.2.2", "@awesome-cordova-plugins/android-permissions": "^6.8.0", "@awesome-cordova-plugins/approov-advanced-http": "^6.8.0", "@awesome-cordova-plugins/camera": "^6.8.0", "@awesome-cordova-plugins/camera-preview": "^6.9.0", "@awesome-cordova-plugins/chooser": "^6.8.0", "@awesome-cordova-plugins/core": "^6.8.0", "@awesome-cordova-plugins/device": "^6.8.0", "@awesome-cordova-plugins/diagnostic": "^6.8.0", "@awesome-cordova-plugins/document-viewer": "^6.8.0", "@awesome-cordova-plugins/email-composer": "^6.8.0", "@awesome-cordova-plugins/file": "^6.8.0", "@awesome-cordova-plugins/file-opener": "^6.8.0", "@awesome-cordova-plugins/file-path": "^6.8.0", "@awesome-cordova-plugins/file-transfer": "^6.8.0", "@awesome-cordova-plugins/geolocation": "^6.8.0", "@awesome-cordova-plugins/http": "^6.8.0", "@awesome-cordova-plugins/in-app-browser": "^6.8.0", "@awesome-cordova-plugins/ionic-webview": "^6.8.0", "@awesome-cordova-plugins/keyboard": "^6.8.0", "@awesome-cordova-plugins/location-accuracy": "^6.8.0", "@awesome-cordova-plugins/media-capture": "^6.8.0", "@awesome-cordova-plugins/network": "^6.8.0", "@awesome-cordova-plugins/nfc": "^6.8.0", "@awesome-cordova-plugins/screen-orientation": "^6.8.0", "@awesome-cordova-plugins/unvired-cordova-sdk": "^6.8.0", "@awesome-cordova-plugins/zip": "^6.8.0", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@ionic/angular": "^8.2.7", "@ionic/cli": "^7.2.0", "@ionic/cordova-builders": "^12.1.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@tensorflow/tfjs": "^4.20.0", "cordova": "^12.0.0", "file-saver": "^2.0.5", "ionic-tooltips": "^3.1.0", "ionic4-tooltips": "file:ionic4-tooltips-1.0.0.tgz", "ionicons": "^7.4.0", "jquery": "^3.7.1", "moment": "^2.30.1", "native-run": "^2.0.1", "ngx-image-compress": "^18.1.5", "rxjs": "~7.8.1", "tslib": "^2.7.0", "zone.js": "~0.14.8"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.2", "@angular-eslint/builder": "^18.3.0", "@angular-eslint/eslint-plugin": "^18.3.0", "@angular-eslint/eslint-plugin-template": "^18.3.0", "@angular-eslint/schematics": "^18.3.0", "@angular-eslint/template-parser": "^18.3.0", "@angular/cli": "^18.2.2", "@angular/compiler-cli": "^18.2.2", "@angular/language-service": "^18.2.2", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "~5.1.4", "@types/jquery": "^3.5.30", "@typescript-eslint/eslint-plugin": "^8.4.0", "@typescript-eslint/parser": "^8.4.0", "community-cordova-plugin-nfc": "^1.4.0", "cordova-android": "^13.0.0", "cordova-browser": "^7.0.0", "cordova-electron": "^4.0.0", "cordova-ios": "^7.1.1", "cordova-plugin-add-swift-support": "^2.0.2", "cordova-plugin-advanced-http": "^3.3.1", "cordova-plugin-android-permissions": "^1.1.5", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-androidx-adapter": "^1.1.3", "cordova-plugin-camera": "^8.0.0", "cordova-plugin-camera-preview": "^0.13.0", "cordova-plugin-chooser": "^1.3.2", "cordova-plugin-document-viewer": "^1.0.0", "cordova-plugin-email-composer": "^0.10.1", "cordova-plugin-file": "github:srinidhirao/cordova-plugin-file", "cordova-plugin-file-opener2": "github:Malli1320/cordova-plugin-file-opener2", "cordova-plugin-file-transfer": "^2.0.0", "cordova-plugin-filepath": "^1.6.0", "cordova-plugin-firebase-crash": "^8.0.2", "cordova-plugin-firebase-messaging": "^8.0.1", "cordova-plugin-geolocation": "^5.0.0", "cordova-plugin-image-editor-windows": "file:unvired-cordova-image-editor-windows-10", "cordova-plugin-inappbrowser": "^6.0.0", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "github:nivlempert1/cordova-plugin-ionic-webview#master", "cordova-plugin-measurement": "file:Unvired_Plugins/measurement-plugin", "cordova-plugin-media-capture": "^5.0.0", "cordova-plugin-network-information": "^3.0.0", "cordova-plugin-request-location-accuracy": "^2.3.0", "cordova-plugin-screen-orientation": "^3.0.4", "cordova-plugin-splashscreen": "6.0.2", "cordova-plugin-unvired-device": "^1.0.0", "cordova-plugin-unvired-electron-db": "^0.0.27", "cordova-plugin-unvired-logger": "^0.0.13", "cordova-plugin-unvired-push-sdk": "^0.0.2", "cordova-plugin-unvired-universal-sdk": "file:../unvired-cordova-universal-sdk/build/cordova-plugin-unvired-universal-sdk", "cordova-plugin-zip": "^3.1.0", "cordova-support-android-plugin": "~2.0.4", "cordova.plugins.diagnostic": "^7.1.4", "es6-promise-plugin": "^4.2.2", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-prefer-arrow": "1.2.3", "jasmine-core": "~5.2.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.4"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-splashscreen": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-email-composer": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-filepath": {}, "cordova-plugin-zip": {}, "cordova-plugin-android-permissions": {}, "cordova-plugin-network-information": {}, "cordova-plugin-screen-orientation": {}, "cordova-plugin-document-viewer": {}, "cordova-plugin-request-location-accuracy": {"PLAY_SERVICES_LOCATION_VERSION": "16.+"}, "cordova-plugin-geolocation": {"GPS_REQUIRED": "true"}, "cordova-plugin-chooser": {}, "cordova-plugin-measurement": {}, "cordova.plugins.diagnostic": {"ANDROIDX_VERSION": "1.0.0", "ANDROIDX_APPCOMPAT_VERSION": "1.3.1"}, "cordova-plugin-advanced-http": {"ANDROIDBLACKLISTSECURESOCKETPROTOCOLS": "SSLv3,TLSv1"}, "cordova-plugin-unvired-push-sdk": {}, "cordova-plugin-androidx-adapter": {}, "cordova-plugin-androidx": {}, "cordova-plugin-media-capture": {}, "community-cordova-plugin-nfc": {}, "cordova-plugin-file-transfer": {}, "cordova-plugin-camera-preview": {}, "cordova-plugin-image-editor-windows": {}, "cordova-plugin-camera": {"ANDROIDX_CORE_VERSION": "1.6.+"}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-unvired-electron-db": {}, "cordova-plugin-unvired-device": {}, "cordova-plugin-unvired-logger": {}, "cordova-plugin-file-opener2": {}, "cordova-plugin-file": {"ANDROIDX_WEBKIT_VERSION": "1.4.0"}, "cordova-plugin-unvired-universal-sdk": {}, "cordova-plugin-firebase-crash": {"CRASHLYTICS_COLLECTION_ENABLED": "true", "ANDROID_FIREBASE_BOM_VERSION": "32.5.0"}}, "platforms": ["browser", "ios", "electron", "android"]}, "volta": {"node": "20.13.0"}}